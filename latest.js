// متغيرات للتحديث التلقائي والتصفح
let autoUpdateInterval = null;
const UPDATE_INTERVAL = 10000; // التحديث كل دقيقة (بالملي ثانية)
let lastUpdateTime = null;
let currentPage = 1;
const perPage = 20;

// متغيرات للتخزين المؤقت
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 دقائق بالملي ثانية
const pageCache = {}; // كاش للصفحات

// وظيفة جلب بيانات الروايات
function fetchNovels(page = 1, forceRefresh = false) {
    const novelsContainer = document.getElementById("novels");
    const noChaptersSection = document.getElementById("no-chapters-section");
    const isNoChaptersSectionVisible = !noChaptersSection.classList.contains('hidden');
    const cacheKey = `page_${page}`;
    const cachedData = pageCache[cacheKey];
    const now = new Date().getTime();

    // استخدام البيانات المخزنة مؤقتًا إذا كانت متوفرة
    if (!forceRefresh && cachedData && (now - cachedData.timestamp < CACHE_EXPIRY)) {
        console.log(`استخدام البيانات المخزنة مؤقتًا للصفحة ${page}`);
        renderPageData(cachedData.data, isNoChaptersSectionVisible);
        return;
    }

    // إظهار مؤشر تحميل صغير بدلاً من شاشة التحميل الكاملة
    if (novelsContainer.children.length === 0) {
        novelsContainer.innerHTML = `
            <div style="text-align: center; padding: 10px;">
                <div class="loading-indicator" style="width: 30px; height: 30px; border-width: 3px; border-color: rgba(52, 152, 219, 0.3); border-top-color: #3498db; display: inline-block;"></div>
            </div>
        `;
    } else {
        // إضافة مؤشر تحميل صغير في أعلى قائمة الروايات
        const smallLoader = document.createElement('div');
        smallLoader.id = 'small-loading';
        smallLoader.style.textAlign = 'center';
        smallLoader.style.padding = '5px';
        smallLoader.innerHTML = '<div class="loading-indicator" style="width: 20px; height: 20px; display: inline-block;"></div>';
        novelsContainer.insertBefore(smallLoader, novelsContainer.firstChild);
    }

    fetch(`http://*************:9090/api/manager?page=${page}&per_page=${perPage}`)
        .then(res => {
            if (!res.ok) {
                throw new Error(`فشل الطلب: ${res.status} - ${res.statusText}`);
            }
            return res.json();
        })
        .then(data => {
            // تخزين البيانات في الكاش
            pageCache[cacheKey] = {
                data: data,
                timestamp: now
            };

            // إزالة مؤشر التحميل الصغير
            const smallLoader = document.getElementById('small-loading');
            if (smallLoader) {
                smallLoader.remove();
            }

            // عرض البيانات
            renderPageData(data, isNoChaptersSectionVisible);
        })
        .catch(error => {
            console.error('Error fetching data:', error);

            // إزالة مؤشر التحميل الصغير
            const smallLoader = document.getElementById('small-loading');
            if (smallLoader) {
                smallLoader.remove();
            }

            // عرض رسالة خطأ مختصرة
            novelsContainer.innerHTML = `
                <div style="text-align: center; padding: 15px;">
                    <div style="color: #e74c3c; font-size: 24px; margin-bottom: 10px;">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h3 style="color: #e74c3c; margin-bottom: 10px;">عذراً، حدث خطأ أثناء تحميل البيانات</h3>
                    <button onclick="fetchNovels(${page}, true)" class="control-btn refresh">
                        <i class="fas fa-sync-alt"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// وظيفة تحديث لوحة المعلومات الإحصائية
function updateDashboard(data) {
    const totalWorks = data.total_works;
    document.getElementById('total-works').textContent = totalWorks;

    let chaptersToday = 0;
    let pendingWorks = 0;
    let delayedWorks = 0;
    const today = new Date().toDateString();

    data.works.forEach(work => {
        work.latest_chapters.forEach(chapter => {
            if (!chapter.publish_datetime) {
                console.error('publish_datetime غير موجود:', chapter);
                return;
            }
            // تحويل تنسيق التاريخ من RFC 2822
            const publishDateTime = new Date(chapter.publish_datetime);
            if (isNaN(publishDateTime.getTime())) {
                console.error('تنسيق publish_datetime غير صالح:', chapter.publish_datetime);
                return;
            }
            const chapterDate = publishDateTime.toDateString();
            if (chapterDate === today) {
                chaptersToday++;
            }
        });

        if (work.latest_chapters.length > 0) {
            pendingWorks++;

            // تحويل تنسيق التاريخ من RFC 2822
            const publishDateTime = new Date(work.latest_chapters[0].publish_datetime);
            if (isNaN(publishDateTime.getTime())) {
                console.error('تنسيق publish_datetime غير صالح:', work.latest_chapters[0].publish_datetime);
                return;
            }
            const lastUpdateDate = publishDateTime;
            const currentDate = new Date();
            const daysSinceLastUpdate = Math.floor((currentDate - lastUpdateDate) / (1000 * 60 * 60 * 24));

            if (daysSinceLastUpdate > 30) {
                delayedWorks++;
            }
        }
    });

    document.getElementById('chapters-today').textContent = chaptersToday;
    document.getElementById('pending-works').textContent = pendingWorks;
    document.getElementById('delayed-works').textContent = delayedWorks;

    let lastChapterUpdate = '-';
    if (data.works.length > 0 && data.works[0].latest_chapters.length > 0) {
        // تحويل تنسيق التاريخ من RFC 2822
        const publishDateTime = new Date(data.works[0].latest_chapters[0].publish_datetime);
        if (!isNaN(publishDateTime.getTime())) {
            lastChapterUpdate = publishDateTime.toLocaleString('ar', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            console.error('تنسيق publish_datetime غير صالح لآخر تحديث:', data.works[0].latest_chapters[0].publish_datetime);
        }
    }
    document.getElementById('last-chapter-update').textContent = lastChapterUpdate;
}

// وظيفة عرض بيانات الصفحة
function renderPageData(data, keepNoChaptersSectionVisible = false) {
    const novelsContainer = document.getElementById("novels");
    const noChaptersContainer = document.getElementById("no-chapters-novels");
    const noChaptersSection = document.getElementById("no-chapters-section");

    updateDashboard(data);

    novelsContainer.innerHTML = '';

    if (!noChaptersSection.classList.contains('hidden')) {
        noChaptersContainer.innerHTML = '';
    }

    const worksWithChapters = data.works.filter(work => work.latest_chapters.length > 0);
    const worksWithoutChapters = data.works.filter(work => work.latest_chapters.length === 0);

    const chaptersFragment = document.createDocumentFragment();
    worksWithChapters.forEach((novel, index) => {
        const globalIndex = (data.page - 1) * data.per_page + index + 1;
        const card = document.createElement("div");
        card.className = "novel-card";
        const chaptersCount = novel.latest_chapters.length;

        // تحويل تنسيق التاريخ من RFC 2822
        const lastUpdateDate = novel.latest_chapters.length > 0
            ? new Date(novel.latest_chapters[0].publish_datetime)
            : new Date();
        if (isNaN(lastUpdateDate.getTime())) {
            console.error('تنسيق publish_datetime غير صالح للعمل:', novel.name, novel.latest_chapters[0].publish_datetime);
        }
        const currentDate = new Date();
        const daysSinceLastUpdate = Math.floor((currentDate - lastUpdateDate) / (1000 * 60 * 60 * 24));

        let statusClass = 'status-active';
        let statusText = 'نشط';

        if (daysSinceLastUpdate > 30) {
            statusClass = 'status-delayed';
            statusText = 'متأخر';
        } else if (daysSinceLastUpdate > 14) {
            statusClass = 'status-warning';
            statusText = 'بطيء';
        }

        card.innerHTML = `
            <div class="novel-card-header">
                <img src="${novel.image_url}" alt="${novel.name}" loading="lazy">
                <div>
                    <h3>${novel.name}</h3>
                    <div class="novel-meta">
                        <span class="novel-status ${statusClass}">${statusText}</span>
                        <span class="novel-chapters-count">${chaptersCount} فصل</span>
                    </div>
                </div>
            </div>

            <div class="novel-card-content">
                <ul>
                    ${novel.latest_chapters.map(ch => {
                        let formattedDate = 'غير متاح';
                        if (ch.publish_datetime) {
                            // تحويل تنسيق التاريخ من RFC 2822
                            const publishDateTime = new Date(ch.publish_datetime);
                            if (!isNaN(publishDateTime.getTime())) {
                                formattedDate = publishDateTime.toLocaleString('ar', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                });
                            } else {
                                console.error('تنسيق publish_datetime غير صالح للفصل:', ch.chapter_number, ch.publish_datetime);
                            }
                        } else {
                            console.error('publish_datetime غير موجود للفصل:', ch.chapter_number, ch);
                        }
                        return `
                            <li>
                                <strong>الفصل ${ch.chapter_number}:</strong>
                                <a href="#" class="chapter-link" onclick="alert('سيتم فتح الفصل قريبًا');">${ch.chapter_title}</a>
                                ${ch.is_new ? '<span class="new-badge">New</span>' : ''}
                                <br>
                                <small>📅 ${formattedDate}</small>
                            </li>
                        `;
                    }).join('')}
                </ul>
            </div>
            <div class="novel-rank">${globalIndex}</div>
        `;
        chaptersFragment.appendChild(card);
    });
    novelsContainer.appendChild(chaptersFragment);

    const emptyStateElement = document.querySelector('.no-chapters-empty');
    if (emptyStateElement) {
        emptyStateElement.style.display = 'none';
    }

    if (keepNoChaptersSectionVisible && worksWithoutChapters.length > 0 && !noChaptersSection.classList.contains('hidden')) {
        const noChaptersFragment = document.createDocumentFragment();

        worksWithoutChapters.forEach(novel => {
            const card = document.createElement("div");
            card.className = "novel-card no-chapters-card simplified-elegant";
            card.dataset.name = novel.name;
            card.dataset.date = novel.added_date || new Date().toISOString();

            card.innerHTML = `
                <div class="novel-card-header elegant">
                    <img src="${novel.image_url}" alt="${novel.name}" loading="lazy">
                    <div class="novel-title-wrapper">
                        <h3>${novel.name}</h3>
                        <p class="no-chapters-message">ليس هناك فصول بعد</p>
                    </div>
                </div>
            `;
            noChaptersFragment.appendChild(card);
        });

        noChaptersContainer.appendChild(noChaptersFragment);
    }

    updatePagination(data.page, data.total_pages, data.total_works);

    lastUpdateTime = new Date();
    updateLastUpdateTimeDisplay();
}

// وظيفة لتحديث عرض وقت آخر تحديث
function updateLastUpdateTimeDisplay() {
    const updateTimeElement = document.getElementById('last-update-time');
    if (updateTimeElement && lastUpdateTime) {
        const timeString = lastUpdateTime.toLocaleTimeString('ar', { hour12: true });
        updateTimeElement.textContent = timeString;
    }
}

// وظيفة لتحديث أزرار التصفح
function updatePagination(page, totalPages, totalWorks) {
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');
    const paginationInfo = document.getElementById('pagination-info');

    currentPage = parseInt(page);

    prevButton.disabled = currentPage <= 1;
    nextButton.disabled = currentPage >= totalPages;

    if (prevButton.disabled) {
        prevButton.classList.add('disabled');
    } else {
        prevButton.classList.remove('disabled');
    }

    if (nextButton.disabled) {
        nextButton.classList.add('disabled');
    } else {
        nextButton.classList.remove('disabled');
    }

    paginationInfo.textContent = `صفحة ${currentPage} من ${totalPages} (${totalWorks} عمل)`;
    paginationInfo.setAttribute('aria-live', 'polite');

    prevButton.removeEventListener('click', handlePrevPage);
    nextButton.removeEventListener('click', handleNextPage);

    prevButton.addEventListener('click', handlePrevPage);
    nextButton.addEventListener('click', handleNextPage);

    localStorage.setItem('currentPage', currentPage);

    if (page > 1) {
        showMessage(`تم الانتقال إلى الصفحة ${page}`, 'info');
    }

    setTimeout(() => preloadAdjacentPages(), 500);
}

// وظيفة معالجة زر الصفحة السابقة
function handlePrevPage() {
    if (currentPage > 1) {
        const prevButton = document.getElementById('prev-page');
        const originalText = prevButton.textContent;
        prevButton.innerHTML = '<span class="loading-indicator"></span> جاري التحميل';

        fetchNovels(currentPage - 1);

        setTimeout(() => {
            prevButton.textContent = originalText;
        }, 300);
    }
}

// وظيفة معالجة زر الصفحة التالية
function handleNextPage() {
    const totalPages = parseInt(document.getElementById('pagination-info').textContent.match(/من (\d+)/)[1]);
    if (currentPage < totalPages) {
        const nextButton = document.getElementById('next-page');
        const originalText = nextButton.textContent;
        nextButton.innerHTML = '<span class="loading-indicator"></span> جاري التحميل';

        fetchNovels(currentPage + 1);

        setTimeout(() => {
            nextButton.textContent = originalText;
        }, 300);
    }
}

// وظيفة تحميل مسبق للصفحات المجاورة
function preloadAdjacentPages() {
    const totalPages = parseInt(document.getElementById('pagination-info').textContent.match(/من (\d+)/)[1]);

    if (currentPage < totalPages) {
        setTimeout(() => {
            const nextPageKey = `page_${currentPage + 1}`;
            if (!pageCache[nextPageKey]) {
                console.log(`تحميل مسبق للصفحة ${currentPage + 1}`);
                fetch(`http://*************:9090/api/manager?page=${currentPage + 1}&per_page=${perPage}`)
                    .then(res => res.json())
                    .then(data => {
                        pageCache[nextPageKey] = {
                            data: data,
                            timestamp: new Date().getTime()
                        };
                    })
                    .catch(err => console.error('خطأ في التحميل المسبق:', err));
            }
        }, 1000);
    }

    if (currentPage > 1) {
        setTimeout(() => {
            const prevPageKey = `page_${currentPage - 1}`;
            if (!pageCache[prevPageKey]) {
                console.log(`تحميل مسبق للصفحة ${currentPage - 1}`);
                fetch(`http://*************:9090/api/manager?page=${currentPage - 1}&per_page=${perPage}`)
                    .then(res => res.json())
                    .then(data => {
                        pageCache[prevPageKey] = {
                            data: data,
                            timestamp: new Date().getTime()
                        };
                    })
                    .catch(err => console.error('خطأ في التحميل المسبق:', err));
            }
        }, 2000);
    }
}

// وظيفة لعرض رسائل للمستخدم
function showMessage(message, type = 'info') {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.textContent = message;

    const container = document.querySelector('.container');
    container.insertBefore(messageElement, container.firstChild);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => {
            messageElement.remove();
        }, 500);
    }, 3000);
}

// وظيفة لتبديل حالة التحديث التلقائي
function toggleAutoUpdate() {
    const toggleBtn = document.getElementById('toggle-auto-update');
    if (autoUpdateInterval) {
        clearInterval(autoUpdateInterval);
        autoUpdateInterval = null;
        toggleBtn.textContent = 'تشغيل التحديث التلقائي';
        toggleBtn.classList.remove('active');
    } else {
        fetchNovels(currentPage);
        autoUpdateInterval = setInterval(() => fetchNovels(currentPage), UPDATE_INTERVAL);
        toggleBtn.textContent = 'إيقاف التحديث التلقائي';
        toggleBtn.classList.add('active');
    }
}

// متغير لتخزين نوع الخدمة الحالية
let currentService = 'default';

// وظائف إضافة رابط جديد
function showAddLinkForm(service = 'default') {
    currentService = service;
    document.getElementById('add-link-form').style.display = 'flex';
    document.getElementById('new-link').focus();
    document.getElementById('add-link-status').textContent = '';
    document.getElementById('add-link-status').className = 'add-link-status';
}

function hideAddLinkForm() {
    document.getElementById('add-link-form').style.display = 'none';
    document.getElementById('new-link').value = '';
    currentService = 'default';
}

async function addNewLink() {
    const linkInput = document.getElementById('new-link');
    const statusElement = document.getElementById('add-link-status');
    const link = linkInput.value.trim();

    if (!link) {
        statusElement.textContent = 'الرجاء إدخال رابط';
        statusElement.className = 'add-link-status error';
        return;
    }

    if (!link.startsWith('http')) {
        statusElement.textContent = 'الرجاء إدخال رابط صحيح يبدأ بـ http أو https';
        statusElement.className = 'add-link-status error';
        return;
    }

    try {
        statusElement.textContent = 'جاري إضافة الرابط...';
        statusElement.className = 'add-link-status';
        statusElement.style.display = 'block';

        const serverUrl = 'http://*************:9091/control_script';
        const response = await fetch(serverUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: 'add_link',
                link: link,
                service: currentService
            }),
        });

        if (!response.ok) {
            throw new Error(`فشل الطلب: ${response.status} - ${response.statusText}`);
        }

        const result = await response.json();

        if (result.status === 'تمت الإضافة بنجاح') {
            statusElement.textContent = result.details;
            statusElement.className = 'add-link-status success';
            linkInput.value = '';
            setTimeout(() => {
                hideAddLinkForm();
            }, 3000);
        } else {
            statusElement.textContent = result.details || 'حدث خطأ أثناء إضافة الرابط';
            statusElement.className = 'add-link-status error';
        }
    } catch (error) {
        console.error('خطأ:', error);
        statusElement.textContent = error.message || 'حدث خطأ أثناء إضافة الرابط';
        statusElement.className = 'add-link-status error';
    }
}

// متغير لتخزين بيانات الرواية التي تم جلبها
let fetchedNovelData = null;

// وظائف إضافة معلومات الرواية
function showNovelInfoForm(service = 'default') {
    currentService = service;
    document.getElementById('novel-info-form').style.display = 'flex';
    document.getElementById('novel-url').focus();
    document.getElementById('novel-info-status').textContent = '';
    document.getElementById('novel-info-status').className = 'add-link-status';
    document.getElementById('novel-info-step-1').style.display = 'block';
    document.getElementById('novel-info-step-2').style.display = 'none';
}

function hideNovelInfoForm() {
    document.getElementById('novel-info-form').style.display = 'none';
    document.getElementById('novel-url').value = '';
    document.getElementById('novel-info-preview').innerHTML = '';
    fetchedNovelData = null;
    currentService = 'default';
}

function backToNovelInfoStep1() {
    document.getElementById('novel-info-step-1').style.display = 'block';
    document.getElementById('novel-info-step-2').style.display = 'none';
    document.getElementById('novel-info-status').textContent = '';
    document.getElementById('novel-info-status').className = 'add-link-status';
}

async function fetchNovelInfo() {
    const urlInput = document.getElementById('novel-url');
    const statusElement = document.getElementById('novel-info-status');
    const url = urlInput.value.trim();

    if (!url) {
        statusElement.textContent = 'الرجاء إدخال رابط';
        statusElement.className = 'add-link-status error';
        return;
    }

    if (!url.startsWith('http')) {
        statusElement.textContent = 'الرجاء إدخال رابط صحيح يبدأ بـ http أو https';
        statusElement.className = 'add-link-status error';
        return;
    }

    try {
        statusElement.innerHTML = '<div style="text-align: center;"><div class="loading-indicator" style="width: 20px; height: 20px; display: inline-block; margin-bottom: 10px;"></div><div>جاري جلب معلومات الرواية...</div><div style="font-size: 0.9em; margin-top: 5px; color: #777;">قد تستغرق هذه العملية وقتًا طويلًا، يرجى الانتظار</div></div>';
        statusElement.className = 'add-link-status';
        statusElement.style.display = 'block';

        const serverUrl = 'http://*************:9091/control_script';
        const response = await fetch(serverUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: 'novel_info',
                action: 'fetch',
                url: url,
                service: currentService
            }),
        });

        if (!response.ok) {
            throw new Error(`فشل الطلب: ${response.status} - ${response.statusText}`);
        }

        const result = await response.json();

        if (result.status.includes('تم جلب')) {
            fetchedNovelData = result.novel_data;
            const previewElement = document.getElementById('novel-info-preview');
            const novel = fetchedNovelData.novel;

            let previewHTML = '';
            if (result.details) {
                let scriptPath;
                if (currentService === 'rewayat') {
                    scriptPath = '/root/novel-scraper/rewayat-club/Novel_info.py';
                } else if (currentService === 'arno') {
                    scriptPath = '/root/novel-scraper/ar-no/Novel_info.py';
                } else {
                    scriptPath = '/root/novel-scraper/Space_novels/Novel_info.py';
                }
                previewHTML += `<div class="info-item" style="color: green; margin-bottom: 10px;">تم جلب البيانات باستخدام سكربت: ${scriptPath}</div>`;
            }

            if (novel.image_url) {
                previewHTML += `<img src="${novel.image_url}" alt="${novel.name}">`;
            }
            previewHTML += `<h5>${novel.name}</h5>`;

            if (novel.basic_info) {
                const basicInfo = novel.basic_info;
                previewHTML += '<div class="info-section">';
                if (basicInfo.author) {
                    previewHTML += `<div class="info-item"><span class="info-label">المؤلف: </span>${basicInfo.author}</div>`;
                }
                if (basicInfo.translator) {
                    previewHTML += `<div class="info-item"><span class="info-label">المترجم: </span>${basicInfo.translator}</div>`;
                }
                if (basicInfo.status) {
                    previewHTML += `<div class="info-item"><span class="info-label">الحالة: </span>${basicInfo.status}</div>`;
                }
                if (basicInfo.genre) {
                    previewHTML += `<div class="info-item"><span class="info-label">النوع: </span>${basicInfo.genre}</div>`;
                }
                if (basicInfo.year) {
                    previewHTML += `<div class="info-item"><span class="info-label">سنة الإصدار: </span>${basicInfo.year}</div>`;
                }
                previewHTML += '</div>';
            }

            if (novel.tags && novel.tags.length > 0) {
                previewHTML += `<div class="info-item"><span class="info-label">التصنيفات: </span>${novel.tags.join(', ')}</div>`;
            }

            if (novel.views && novel.views.total_views) {
                previewHTML += `<div class="info-item"><span class="info-label">المشاهدات: </span>${novel.views.total_views.toLocaleString()}</div>`;
            }

            if (novel.story) {
                previewHTML += `<div class="info-item"><span class="info-label">القصة: </span><p>${novel.story}</p></div>`;
            }

            previewElement.innerHTML = previewHTML;

            document.getElementById('novel-info-step-1').style.display = 'none';
            document.getElementById('novel-info-step-2').style.display = 'block';
            statusElement.style.display = 'none';
        } else {
            statusElement.textContent = result.details || 'حدث خطأ أثناء جلب معلومات الرواية';
            statusElement.className = 'add-link-status error';
        }
    } catch (error) {
        console.error('خطأ:', error);
        statusElement.textContent = error.message || 'حدث خطأ أثناء جلب معلومات الرواية';
        statusElement.className = 'add-link-status error';
    }
}

async function addNovelInfo() {
    const statusElement = document.getElementById('novel-info-status');

    if (!fetchedNovelData) {
        statusElement.textContent = 'لم يتم جلب معلومات الرواية بعد';
        statusElement.className = 'add-link-status error';
        statusElement.style.display = 'block';
        return;
    }

    try {
        statusElement.innerHTML = '<div style="text-align: center;"><div class="loading-indicator" style="width: 20px; height: 20px; display: inline-block; margin-bottom: 10px;"></div><div>جاري إضافة معلومات الرواية إلى قاعدة البيانات...</div><div style="font-size: 0.9em; margin-top: 5px; color: #777;">قد تستغرق هذه العملية وقتًا طويلًا، يرجى الانتظار</div></div>';
        statusElement.className = 'add-link-status';
        statusElement.style.display = 'block';

        const serverUrl = 'http://*************:9091/control_script';
        const response = await fetch(serverUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: 'novel_info',
                action: 'add',
                novel_data: fetchedNovelData,
                service: currentService
            }),
        });

        if (!response.ok) {
            throw new Error(`فشل الطلب: ${response.status} - ${response.statusText}`);
        }

        const result = await response.json();

        if (result.status.includes('تمت إضافة')) {
            statusElement.textContent = result.details || 'تمت إضافة معلومات الرواية بنجاح';
            statusElement.className = 'add-link-status success';
            setTimeout(() => {
                hideNovelInfoForm();
            }, 3000);
        } else {
            statusElement.textContent = result.details || 'حدث خطأ أثناء إضافة معلومات الرواية';
            statusElement.className = 'add-link-status error';
        }
    } catch (error) {
        console.error('خطأ:', error);
        statusElement.textContent = error.message || 'حدث خطأ أثناء إضافة معلومات الرواية';
        statusElement.className = 'add-link-status error';
    }
}

// وظيفة لإرسال الأوامر إلى السيرفر
async function sendCommand(command, service = 'default') {
    const statusText = document.getElementById(
        service === 'rewayat' ? 'rewayat-status-text' :
        service === 'arno' ? 'arno-status-text' : 'status-text'
    );
    const statusDetails = document.getElementById(
        service === 'rewayat' ? 'rewayat-status-details' :
        service === 'arno' ? 'arno-status-details' : 'status-details'
    );

    try {
        statusText.textContent = 'جاري التنفيذ...';
        statusDetails.innerHTML = '<div style="text-align: center; padding: 10px;"><div class="loading-indicator" style="width: 20px; height: 20px; display: inline-block; margin-bottom: 10px;"></div><div>جاري تنفيذ الأمر...</div><div style="font-size: 0.9em; margin-top: 5px; color: #777;">قد تستغرق بعض العمليات وقتًا طويلًا، يرجى الانتظار</div></div>';
        statusDetails.style.display = 'block';

        const serverUrl = 'http://*************:9091/control_script';
        console.log('محاولة الاتصال بالعنوان:', serverUrl);

        const response = await fetch(serverUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ command, service }),
            timeout: 10000
        });

        if (!response.ok) {
            throw new Error(`فشل الطلب: ${response.status} - ${response.statusText}`);
        }

        const result = await response.json();

        statusText.textContent = result.status;

        if (result.details) {
            statusDetails.textContent = result.details;
            statusDetails.style.display = 'block';
        } else {
            statusDetails.style.display = 'none';
        }

        return result;
    } catch (error) {
        console.error('خطأ:', error);
        statusText.textContent = 'حدث خطأ في الاتصال';

        let errorDetails = error.message;
        errorDetails += '\n\nتأكد من:\n';
        errorDetails += `1. تأكد من تشغيل ملف control_script.py على السيرفر\n`;
        errorDetails += `2. تأكد من أن الخادم يعمل على المنفذ 9091\n`;
        errorDetails += `3. تأكد من أن إعدادات CORS تسمح بالوصول\n`;
        errorDetails += `4. تأكد من أن مستخدم Python لديه صلاحيات sudo\n`;
        errorDetails += `\nمحاولة الاتصال بالعنوان: ${serverUrl}`;

        statusDetails.textContent = errorDetails;
        statusDetails.style.display = 'block';
    }
}

// وظيفة البحث عن الروايات
function searchNovels() {
    const searchTerm = document.getElementById('search-input').value.trim().toLowerCase();
    if (!searchTerm) {
        fetchNovels(currentPage);
        return;
    }

    const novelsContainer = document.getElementById("novels");
    const noChaptersContainer = document.getElementById("no-chapters-novels");
    novelsContainer.innerHTML = '<p style="text-align: center; color: #3498db;"><span class="loading-indicator"></span>جاري البحث...</p>';
    noChaptersContainer.innerHTML = '';

    fetch(`http://*************:9090/api/manager?page=1&per_page=100`)
        .then(res => res.json())
        .then(data => {
            console.log('بيانات البحث المستلمة:', JSON.stringify(data, null, 2));
            novelsContainer.innerHTML = '';
            noChaptersContainer.innerHTML = '';

            const filteredWorks = data.works.filter(work =>
                work.name.toLowerCase().includes(searchTerm)
            );

            if (filteredWorks.length === 0) {
                novelsContainer.innerHTML = '<p style="text-align: center; color: #e74c3c;">لم يتم العثور على نتائج للبحث</p>';
                return;
            }

            const worksWithChapters = filteredWorks.filter(work => work.latest_chapters.length > 0);
            const worksWithoutChapters = filteredWorks.filter(work => work.latest_chapters.length === 0);

            worksWithChapters.forEach((novel, index) => {
                const card = document.createElement("div");
                card.className = "novel-card";
                card.innerHTML = `
                    <div class="novel-card-header">
                        <img src="${novel.image_url}" alt="${novel.name}" loading="lazy">
                        <div>
                            <h3>${novel.name}</h3>
                        </div>
                    </div>
                    <div class="novel-card-content">
                        <ul>
                            ${novel.latest_chapters.map(ch => {
                                let formattedDate = 'غير متاح';
                                if (ch.publish_datetime) {
                                    const publishDateTime = new Date(ch.publish_datetime);
                                    if (!isNaN(publishDateTime.getTime())) {
                                        formattedDate = publishDateTime.toLocaleString('ar', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        });
                                    } else {
                                        console.error('تنسيق publish_datetime غير صالح للفصل:', ch.chapter_number, ch.publish_datetime);
                                    }
                                } else {
                                    console.error('publish_datetime غير موجود للفصل:', ch.chapter_number, ch);
                                }
                                return `
                                    <li>
                                        <strong>الفصل ${ch.chapter_number}:</strong>
                                        <a href="#" class="chapter-link" onclick="alert('سيتم فتح الفصل قريبًا');">${ch.chapter_title}</a>
                                        ${ch.is_new ? '<span class="new-badge">New</span>' : ''}
                                        <br>
                                        <small>📅 ${formattedDate}</small>
                                    </li>
                                `;
                            }).join('')}
                        </ul>
                    </div>
                    <div class="novel-rank">${index + 1}</div>
                `;
                novelsContainer.appendChild(card);
            });

            worksWithoutChapters.forEach((novel, index) => {
                const card = document.createElement("div");
                card.className = "novel-card";
                card.innerHTML = `
                    <div class="novel-card-header">
                        <img src="${novel.image_url}" alt="${novel.name}" loading="lazy">
                        <div>
                            <h3>${novel.name}</h3>
                        </div>
                    </div>
                    <div class="novel-card-content">
                        <p style="color: #7f8c8d; text-align: center;">لا توجد فصول متاحة حاليًا</p>
                    </div>
                    <div class="novel-rank">${worksWithChapters.length + index + 1}</div>
                `;
                noChaptersContainer.appendChild(card);
            });

            document.getElementById('pagination-info').textContent = `تم العثور على ${filteredWorks.length} نتيجة`;
            document.getElementById('prev-page').disabled = true;
            document.getElementById('next-page').disabled = true;
        })
        .catch(error => {
            console.error('Error searching data:', error);
            novelsContainer.innerHTML = '<p style="text-align: center; color: #e74c3c;">عذراً، حدث خطأ أثناء البحث</p>';
            noChaptersContainer.innerHTML = '';
        });
}

// وظيفة تبديل الوضع المظلم
function toggleDarkMode() {
    const htmlElement = document.documentElement;
    const isDarkMode = htmlElement.getAttribute('data-theme') === 'dark';
    const themeToggleIcon = document.querySelector('#theme-toggle i');

    if (isDarkMode) {
        htmlElement.removeAttribute('data-theme');
        themeToggleIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'light');
    } else {
        htmlElement.setAttribute('data-theme', 'dark');
        themeToggleIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'dark');
    }
}

// وظيفة التحقق من الوضع المظلم المحفوظ
function checkSavedTheme() {
    const savedTheme = localStorage.getItem('theme');
    const htmlElement = document.documentElement;
    const themeToggleIcon = document.querySelector('#theme-toggle i');

    if (savedTheme === 'dark') {
        htmlElement.setAttribute('data-theme', 'dark');
        themeToggleIcon.className = 'fas fa-sun';
    }
}

// وظيفة التحقق من حالة زر العودة لأعلى الصفحة
function checkScrollPosition() {
    const backToTopButton = document.getElementById('back-to-top');
    if (window.scrollY > 300) {
        backToTopButton.classList.add('visible');
    } else {
        backToTopButton.classList.remove('visible');
    }
}

// وظيفة العودة لأعلى الصفحة
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// وظيفة حفظ إعدادات المستخدم
function saveUserSettings() {
    localStorage.setItem('autoUpdate', autoUpdateInterval !== null);
    localStorage.setItem('currentPage', currentPage);
}

// وظيفة استعادة إعدادات المستخدم
function loadUserSettings() {
    const autoUpdateEnabled = localStorage.getItem('autoUpdate') === 'true';
    if (autoUpdateEnabled && !autoUpdateInterval) {
        toggleAutoUpdate();
    }

    const savedPage = parseInt(localStorage.getItem('currentPage'));
    if (savedPage && !isNaN(savedPage)) {
        currentPage = savedPage;
    }
}

// وظيفة تبديل عرض لوحة التحكم الافتراضية
function toggleDefaultPanel() {
    const panel = document.getElementById('default-control-panel');
    const button = document.getElementById('toggle-default-panel');

    panel.classList.toggle('hidden');
    button.classList.toggle('active');

    if (!panel.classList.contains('hidden')) {
        document.getElementById('rewayat-control-panel').classList.add('hidden');
        document.getElementById('toggle-rewayat-panel').classList.remove('active');
        document.getElementById('arno-control-panel').classList.add('hidden');
        document.getElementById('toggle-arno-panel').classList.remove('active');

        sendCommand('status');
    }
}

// وظيفة تبديل عرض لوحة التحكم rewayat
function toggleRewayatPanel() {
    const panel = document.getElementById('rewayat-control-panel');
    const button = document.getElementById('toggle-rewayat-panel');

    panel.classList.toggle('hidden');
    button.classList.toggle('active');

    if (!panel.classList.contains('hidden')) {
        document.getElementById('default-control-panel').classList.add('hidden');
        document.getElementById('toggle-default-panel').classList.remove('active');
        document.getElementById('arno-control-panel').classList.add('hidden');
        document.getElementById('toggle-arno-panel').classList.remove('active');

        sendCommand('status', 'rewayat');
    }
}

// وظيفة تبديل عرض لوحة التحكم ar-no
function toggleArnoPanel() {
    const panel = document.getElementById('arno-control-panel');
    const button = document.getElementById('toggle-arno-panel');

    panel.classList.toggle('hidden');
    button.classList.toggle('active');

    if (!panel.classList.contains('hidden')) {
        document.getElementById('default-control-panel').classList.add('hidden');
        document.getElementById('toggle-default-panel').classList.remove('active');
        document.getElementById('rewayat-control-panel').classList.add('hidden');
        document.getElementById('toggle-rewayat-panel').classList.remove('active');

        sendCommand('status', 'arno');
    }
}

// وظيفة تبديل عرض قسم الأعمال بدون فصول
function toggleNoChaptersSection() {
    const section = document.getElementById('no-chapters-section');
    const button = document.getElementById('toggle-no-chapters');
    const emptyStateElement = document.querySelector('.no-chapters-empty');

    section.classList.toggle('hidden');
    button.classList.toggle('active');

    if (!section.classList.contains('hidden')) {
        if (emptyStateElement) {
            emptyStateElement.style.display = 'none';
        }

        if (document.getElementById('no-chapters-novels').children.length === 0) {
            showSimplifiedNoChaptersNovels();
        }
    }

    localStorage.setItem('noChaptersSectionVisible', !section.classList.contains('hidden'));
}

// وظيفة عرض الأعمال بدون فصول بشكل مبسط وأنيق
function showSimplifiedNoChaptersNovels() {
    const noChaptersContainer = document.getElementById("no-chapters-novels");
    const emptyStateElement = document.querySelector('.no-chapters-empty');

    if (emptyStateElement) {
        emptyStateElement.style.display = 'none';
    }

    noChaptersContainer.innerHTML = '<div style="text-align: center; padding: 5px;"><div class="loading-indicator" style="width: 20px; height: 20px; display: inline-block;"></div></div>';

    fetch(`http://*************:9090/api/manager?page=1&per_page=100`)
        .then(res => res.json())
        .then(data => {
            noChaptersContainer.innerHTML = '';

            const worksWithoutChapters = data.works.filter(work => work.latest_chapters.length === 0);

            if (worksWithoutChapters.length === 0) {
                noChaptersContainer.innerHTML = '<p style="text-align: center; color: #e74c3c;">لا توجد أعمال بدون فصول</p>';
                return;
            }

            const fragment = document.createDocumentFragment();

            worksWithoutChapters.forEach(novel => {
                const card = document.createElement("div");
                card.className = "novel-card no-chapters-card simplified-elegant";
                card.dataset.name = novel.name;
                card.dataset.date = novel.added_date || new Date().toISOString();

                card.innerHTML = `
                    <div class="novel-card-header elegant">
                        <img src="${novel.image_url}" alt="${novel.name}" loading="lazy">
                        <div class="novel-title-wrapper">
                            <h3>${novel.name}</h3>
                            <p class="no-chapters-message">ليس هناك فصول بعد</p>
                        </div>
                    </div>
                `;

                fragment.appendChild(card);
            });

            noChaptersContainer.appendChild(fragment);
        })
        .catch(error => {
            console.error('Error fetching works without chapters:', error);
            noChaptersContainer.innerHTML = '<p style="text-align: center; color: #e74c3c;">عذراً، حدث خطأ أثناء تحميل البيانات</p>';
        });
}

// تشغيل التحديث التلقائي وإعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkSavedTheme();
    loadUserSettings();

    const noChaptersSectionVisible = localStorage.getItem('noChaptersSectionVisible') === 'true';
    if (noChaptersSectionVisible) {
        const section = document.getElementById('no-chapters-section');
        const button = document.getElementById('toggle-no-chapters');

        section.classList.remove('hidden');
        button.classList.add('active');
    }

    setTimeout(() => {
        fetchNovels(currentPage);

        if (noChaptersSectionVisible) {
            showSimplifiedNoChaptersNovels();
        }
    }, 100);

    document.getElementById('theme-toggle').addEventListener('click', toggleDarkMode);
    document.getElementById('back-to-top').addEventListener('click', scrollToTop);
    document.getElementById('search-button').addEventListener('click', searchNovels);
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchNovels();
        }
    });

    document.getElementById('toggle-default-panel').addEventListener('click', toggleDefaultPanel);
    document.getElementById('toggle-rewayat-panel').addEventListener('click', toggleRewayatPanel);
    document.getElementById('toggle-arno-panel').addEventListener('click', toggleArnoPanel);
    document.getElementById('toggle-no-chapters').addEventListener('click', toggleNoChaptersSection);

    document.getElementById('sort-by-name').addEventListener('click', () => sortNoChaptersNovels('name'));
    document.getElementById('sort-by-date').addEventListener('click', () => sortNoChaptersNovels('date'));
    document.getElementById('show-all-no-chapters').addEventListener('click', showAllNoChaptersNovels);

    window.addEventListener('scroll', checkScrollPosition);
    window.addEventListener('beforeunload', saveUserSettings);

    // مستمعي الأحداث للوحة التحكم الافتراضية
    document.getElementById('start-script').addEventListener('click', () => sendCommand('start'));
    document.getElementById('stop-script').addEventListener('click', () => sendCommand('stop'));
    document.getElementById('restart-script').addEventListener('click', () => sendCommand('restart'));
    document.getElementById('status-script').addEventListener('click', () => sendCommand('status'));
    document.getElementById('current-script').addEventListener('click', () => sendCommand('current'));
    document.getElementById('novel-info-btn').addEventListener('click', () => showNovelInfoForm('default'));
    document.getElementById('add-link-btn').addEventListener('click', () => showAddLinkForm('default'));

    // مستمعي الأحداث للوحة التحكم rewayat
    document.getElementById('start-rewayat').addEventListener('click', () => sendCommand('start', 'rewayat'));
    document.getElementById('stop-rewayat').addEventListener('click', () => sendCommand('stop', 'rewayat'));
    document.getElementById('restart-rewayat').addEventListener('click', () => sendCommand('restart', 'rewayat'));
    document.getElementById('reload-rewayat').addEventListener('click', () => sendCommand('reload', 'rewayat'));
    document.getElementById('status-rewayat').addEventListener('click', () => sendCommand('status', 'rewayat'));
    document.getElementById('current-rewayat').addEventListener('click', () => sendCommand('current', 'rewayat'));
    document.getElementById('novel-info-rewayat').addEventListener('click', () => showNovelInfoForm('rewayat'));
    document.getElementById('add-link-rewayat').addEventListener('click', () => showAddLinkForm('rewayat'));

    // مستمعي الأحداث للوحة التحكم ar-no
    document.getElementById('start-arno').addEventListener('click', () => sendCommand('start', 'arno'));
    document.getElementById('stop-arno').addEventListener('click', () => sendCommand('stop', 'arno'));
    document.getElementById('restart-arno').addEventListener('click', () => sendCommand('restart', 'arno'));
    document.getElementById('reload-arno').addEventListener('click', () => sendCommand('reload', 'arno'));
    document.getElementById('status-arno').addEventListener('click', () => sendCommand('status', 'arno'));
    document.getElementById('current-arno').addEventListener('click', () => sendCommand('current', 'arno'));
    document.getElementById('novel-info-arno').addEventListener('click', () => showNovelInfoForm('arno'));
    document.getElementById('add-link-arno').addEventListener('click', () => showAddLinkForm('arno'));
});

// وظيفة للحصول على تصنيفات عشوائية
function getRandomTags() {
    const allTags = [
        'خيال', 'مغامرة', 'رومانسية', 'أكشن', 'دراما', 'غموض', 'رعب', 'كوميديا',
        'تاريخي', 'خيال علمي', 'فانتازيا', 'مدرسي', 'نفسي', 'مأساة', 'إثارة', 'حياة يومية'
    ];

    const count = Math.floor(Math.random() * 3) + 2;
    const selectedTags = [];

    for (let i = 0; i < count; i++) {
        const randomIndex = Math.floor(Math.random() * allTags.length);
        const tag = allTags[randomIndex];

        if (!selectedTags.includes(tag)) {
            selectedTags.push(tag);
        }
    }

    return selectedTags;
}

// وظيفة متابعة رواية
function followNovel(novelName) {
    let followedNovels = JSON.parse(localStorage.getItem('followedNovels') || '[]');

    if (followedNovels.includes(novelName)) {
        followedNovels = followedNovels.filter(name => name !== novelName);
        localStorage.setItem('followedNovels', JSON.stringify(followedNovels));
        showMessage(`تم إلغاء متابعة "${novelName}"`, 'info');
    } else {
        followedNovels.push(novelName);
        localStorage.setItem('followedNovels', JSON.stringify(followedNovels));
        showMessage(`تمت متابعة "${novelName}" بنجاح! سيتم إشعارك عند إضافة فصول جديدة.`, 'success');
    }

    updateFollowButtons();
}

// وظيفة تحديث أزرار المتابعة
function updateFollowButtons() {
    const followedNovels = JSON.parse(localStorage.getItem('followedNovels') || '[]');
    const followButtons = document.querySelectorAll('.follow-btn');

    followButtons.forEach(button => {
        const novelName = button.getAttribute('onclick').match(/'([^']+)'/)[1];

        if (followedNovels.includes(novelName)) {
            button.innerHTML = '<i class="fas fa-bell-slash"></i> إلغاء المتابعة';
            button.classList.add('following');
        } else {
            button.innerHTML = '<i class="fas fa-bell"></i> متابعة';
            button.classList.remove('following');
        }
    });
}

// وظيفة ترتيب الأعمال بدون فصول
function sortNoChaptersNovels(sortBy) {
    const container = document.getElementById('no-chapters-novels');
    const cards = Array.from(container.querySelectorAll('.no-chapters-card'));
    const emptyStateElement = document.querySelector('.no-chapters-empty');

    if (emptyStateElement) {
        emptyStateElement.style.display = 'none';
    }

    if (cards.length === 0) {
        showMessage('لا توجد أعمال بدون فصول للترتيب', 'info');
        return;
    }

    cards.sort((a, b) => {
        if (sortBy === 'name') {
            return a.dataset.name.localeCompare(b.dataset.name);
        } else if (sortBy === 'date') {
            return new Date(b.dataset.date) - new Date(a.dataset.date);
        }
        return 0;
    });

    cards.forEach(card => container.appendChild(card));

    showMessage(`تم ترتيب الأعمال حسب ${sortBy === 'name' ? 'الاسم' : 'تاريخ الإضافة'}`, 'info');
}

// وظيفة عرض جميع الأعمال بدون فصول
function showAllNoChaptersNovels() {
    const noChaptersContainer = document.getElementById("no-chapters-novels");
    const emptyStateElement = document.querySelector('.no-chapters-empty');

    if (emptyStateElement) {
        emptyStateElement.style.display = 'none';
    }

    noChaptersContainer.innerHTML = '<div style="text-align: center; padding: 5px;"><div class="loading-indicator" style="width: 20px; height: 20px; display: inline-block;"></div></div>';

    fetch(`http://*************:9090/api/manager?page=1&per_page=1000`)
        .then(res => res.json())
        .then(data => {
            noChaptersContainer.innerHTML = '';

            const worksWithoutChapters = data.works.filter(work => work.latest_chapters.length === 0);

            if (worksWithoutChapters.length === 0) {
                noChaptersContainer.innerHTML = '<p style="text-align: center; color: #e74c3c;">لا توجد أعمال بدون فصول</p>';
                return;
            }

            const fragment = document.createDocumentFragment();

            worksWithoutChapters.forEach(novel => {
                const card = document.createElement("div");
                card.className = "novel-card no-chapters-card simplified-elegant";
                card.dataset.name = novel.name;
                card.dataset.date = novel.added_date || new Date().toISOString();

                card.innerHTML = `
                    <div class="novel-card-header elegant">
                        <img src="${novel.image_url}" alt="${novel.name}" loading="lazy">
                        <div class="novel-title-wrapper">
                            <h3>${novel.name}</h3>
                            <p class="no-chapters-message">ليس هناك فصول بعد</p>
                        </div>
                    </div>
                `;

                fragment.appendChild(card);
            });

            noChaptersContainer.appendChild(fragment);

            updateFollowButtons();
        })
        .catch(error => {
            console.error('Error fetching all works:', error);
            noChaptersContainer.innerHTML = '<p style="text-align: center; color: #e74c3c;">عذراً، حدث خطأ أثناء تحميل البيانات</p>';
        });
}

// وظيفة عرض تفاصيل الرواية
function showNovelDetails(novelName) {
    const modal = document.createElement('div');
    modal.className = 'novel-details-modal';

    const noChaptersCards = document.querySelectorAll('.no-chapters-card');
    let novelCard = null;

    for (const card of noChaptersCards) {
        if (card.dataset.name === novelName) {
            novelCard = card;
            break;
        }
    }

    if (!novelCard) {
        showMessage('لم يتم العثور على معلومات الرواية', 'error');
        return;
    }

    const imageUrl = novelCard.querySelector('img').src;
    const status = novelCard.querySelector('.novel-status')?.textContent || 'قيد الإعداد';
    const tags = getRandomTags();

    modal.innerHTML = `
        <div class="novel-details-content">
            <button class="close-modal"><i class="fas fa-times"></i></button>
            <div class="novel-details-header">
                <img src="${imageUrl}" alt="${novelName}">
                <div>
                    <h2>${novelName}</h2>
                    <div class="novel-status ${status === 'قيد الترجمة' ? 'active' : ''}">${status}</div>
                </div>
            </div>
            <div class="novel-details-info">
                <div class="info-row">
                    <span class="info-label">التصنيفات:</span>
                    <div class="novel-tags">
                        ${tags.map(tag => `<span class="novel-info-tag">${tag}</span>`).join('')}
                    </div>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span>${status}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الإضافة:</span>
                    <span>${new Date(novelCard.dataset.date).toLocaleDateString('ar')}</span>
                </div>
                <div class="info-description">
                    <p>هذا العمل قيد الإعداد حاليًا وسيتم إضافة فصول جديدة قريبًا. يمكنك متابعة هذا العمل للحصول على إشعارات فور إضافة فصول جديدة.</p>
                </div>
            </div>
            <div class="novel-details-actions">
                <button class="action-btn follow-btn" onclick="followNovel('${novelName}')">
                    <i class="fas fa-bell"></i> متابعة
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    modal.querySelector('.close-modal').addEventListener('click', () => {
        modal.remove();
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });

    const followedNovels = JSON.parse(localStorage.getItem('followedNovels') || '[]');
    const followButton = modal.querySelector('.follow-btn');

    if (followedNovels.includes(novelName)) {
        followButton.innerHTML = '<i class="fas fa-bell-slash"></i> إلغاء المتابعة';
        followButton.classList.add('following');
    }
}