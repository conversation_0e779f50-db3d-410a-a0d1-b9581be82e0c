# routes/latest.py

from flask import Blueprint, jsonify
import mysql.connector
from db import db_config

latest_bp = Blueprint('latest', __name__)

@latest_bp.route('/api/latest', methods=['GET'])
def get_latest_releases():
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        # الاستعلام لجلب كل الأعمال
        cursor.execute("SELECT name, image_url FROM works")
        works = cursor.fetchall()

        result = []

        for work in works:
            # جلب آخر فصلين للعمل بناءً على اسم الرواية وتاريخ النشر
            cursor.execute("""
                SELECT chapter_number, chapter_title, publish_date, publish_datetime
                FROM chapters
                WHERE novel_title = %s
                ORDER BY publish_datetime DESC
                LIMIT 2
            """, (work['name'],))
            chapters = cursor.fetchall()

            result.append({
                "name": work['name'],
                "image_url": work['image_url'],
                "latest_chapters": chapters
            })

        cursor.close()
        conn.close()
        return jsonify(result)

    except mysql.connector.Error as e:
        return jsonify({"status": "error", "message": str(e)}), 500
