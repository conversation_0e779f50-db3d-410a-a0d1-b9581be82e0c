<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>آخر الإصدارات</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="latest.css">
</head>
<body>
  <!-- زر تبديل الوضع المظلم -->
  <div class="theme-toggle" id="theme-toggle" title="تبديل الوضع المظلم">
    <i class="fas fa-moon"></i>
  </div>

  <!-- زر العودة لأعلى الصفحة -->
  <div class="back-to-top" id="back-to-top" title="العودة لأعلى الصفحة">
    <i class="fas fa-arrow-up"></i>
  </div>

  <div class="container">
    <h1>📚 آخر الإصدارات</h1>

    <!-- لوحة المعلومات الإحصائية -->
    <div class="dashboard">
      <div class="stats-container">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-book"></i>
          </div>
          <div class="stat-info">
            <h3 class="stat-value" id="total-works">0</h3>
            <p class="stat-label">إجمالي الأعمال</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-calendar-day"></i>
          </div>
          <div class="stat-info">
            <h3 class="stat-value" id="chapters-today">0</h3>
            <p class="stat-label">فصول اليوم</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-hourglass-half"></i>
          </div>
          <div class="stat-info">
            <h3 class="stat-value" id="pending-works">0</h3>
            <p class="stat-label">أعمال قيد التطوير</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="stat-info">
            <h3 class="stat-value" id="delayed-works">0</h3>
            <p class="stat-label">أعمال متأخرة</p>
          </div>
        </div>
      </div>

      <div class="last-update-info">
        <div class="last-update-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="last-update-text">
          <p>آخر تحديث للفصول: <span id="last-chapter-update">-</span></p>
        </div>
      </div>
    </div>

    <!-- قسم البحث -->
    <div class="search-container">
      <div class="search-box">
        <input type="text" id="search-input" class="search-input" placeholder="ابحث عن رواية...">
        <button id="search-button" class="search-button">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- أزرار التبديل للوحات التحكم -->
    <div class="toggle-buttons">
      <button id="toggle-default-panel" class="toggle-btn">
        <i class="fas fa-cog"></i> لوحة التحكم بالسكربت
      </button>
      <button id="toggle-rewayat-panel" class="toggle-btn">
        <i class="fas fa-cog"></i> لوحة التحكم rewayat
      </button>
      <button id="toggle-arno-panel" class="toggle-btn">
        <i class="fas fa-cog"></i> لوحة التحكم ar-no
      </button>
    </div>

    <!-- قسم التحكم بالسكربت -->
    <div id="default-control-panel" class="control-panel hidden">
      <h2>لوحة التحكم بالسكربت</h2>
      <div class="control-buttons">
        <button id="start-script" class="control-btn start">تشغيل السكربت</button>
        <button id="stop-script" class="control-btn stop">إيقاف السكربت</button>
        <button id="restart-script" class="control-btn restart">إعادة تشغيل</button>
        <button id="status-script" class="control-btn status">عرض الحالة</button>
        <button id="current-script" class="control-btn current">العمل الحالي</button>
        <button id="novel-info-btn" class="control-btn novel-info">إضافة معلومات الرواية</button>
        <button id="add-link-btn" class="control-btn add-link" onclick="showAddLinkForm()">إضافة رابط جديد</button>
      </div>
      <div id="script-status" class="status-display">
        <p>حالة السكربت: <span id="status-text">غير معروفة</span></p>
        <pre id="status-details" class="status-details"></pre>
      </div>
    </div>

    <!-- قسم التحكم بخدمة rewayat -->
    <div id="rewayat-control-panel" class="control-panel hidden">
      <h2>لوحة التحكم rewayat</h2>
      <div class="control-buttons">
        <button id="start-rewayat" class="control-btn start">تشغيل السكربت</button>
        <button id="stop-rewayat" class="control-btn stop">إيقاف السكربت</button>
        <button id="restart-rewayat" class="control-btn restart">إعادة تشغيل</button>
        <button id="reload-rewayat" class="control-btn restart">إعادة تحميل</button>
        <button id="status-rewayat" class="control-btn status">عرض الحالة</button>
        <button id="current-rewayat" class="control-btn current">العمل الحالي</button>
        <button id="novel-info-rewayat" class="control-btn novel-info" onclick="showNovelInfoForm('rewayat')">إضافة معلومات الرواية</button>
        <button id="add-link-rewayat" class="control-btn add-link" onclick="showAddLinkForm('rewayat')">إضافة رابط جديد</button>
      </div>
      <div id="rewayat-status" class="status-display">
        <p>حالة السكربت: <span id="rewayat-status-text">غير معروفة</span></p>
        <pre id="rewayat-status-details" class="status-details"></pre>
      </div>
    </div>

    <!-- قسم التحكم بخدمة ar-no -->
    <div id="arno-control-panel" class="control-panel hidden">
      <h2>لوحة تحكم ar-no</h2>
      <div class="control-buttons">
          <button id="start-arno" class="control-btn start">تشغيل</button>
          <button id="stop-arno" class="control-btn stop">إيقاف</button>
          <button id="restart-arno" class="control-btn restart">إعادة تشغيل</button>
          <button id="reload-arno" class="control-btn reload">إعادة تحميل</button>
          <button id="status-arno" class="control-btn status">الحالة</button>
          <button id="current-arno" class="control-btn current">الحالي</button>
          <button id="novel-info-arno" class="control-btn novel-info">معلومات الرواية</button>
          <button id="add-link-arno" class="control-btn add-link">إضافة رابط</button>
      </div>
      <div class="status-display">
          <p id="arno-status-text">جاري التحقق...</p>
          <pre id="arno-status-details" class="status-details"></pre>
      </div>
  </div>

    <div class="update-controls">
      <div class="update-info">
        <span>آخر تحديث: <span id="last-update-time">—</span></span>
      </div>
      <div class="update-buttons">
        <button id="refresh-now" class="control-btn refresh" onclick="fetchNovels()">تحديث الآن</button>
        <button id="toggle-auto-update" class="control-btn auto-update" onclick="toggleAutoUpdate()">تشغيل التحديث التلقائي</button>
      </div>
    </div>

    <!-- زر تبديل قسم الأعمال بدون فصول -->
    <div class="toggle-buttons">
      <button id="toggle-no-chapters" class="toggle-btn">
        <i class="fas fa-book"></i> الأعمال بدون فصول
      </button>
    </div>

    <!-- قسم الأعمال بدون فصول -->
    <div id="no-chapters-section" class="no-chapters-section hidden">
      <!-- عنوان قسم الأعمال بدون فصول -->
      <div class="section-header no-chapters-header">
        <div class="section-separator">
          <div class="separator-line"></div>
          <div class="separator-icon"><i class="fas fa-book"></i></div>
          <div class="separator-line"></div>
        </div>
        <h2 class="section-title">الأعمال بدون فصول</h2>
      </div>
      <div class="section-description">
        <p>هذه الأعمال قيد الإعداد أو التحضير. يمكنك متابعتها للحصول على إشعار فور إضافة فصول جديدة.</p>
      </div>
      <div class="no-chapters-controls">
        <button id="show-all-no-chapters" class="control-btn info">عرض جميع الأعمال</button>
        <button id="sort-by-date" class="control-btn info">ترتيب حسب تاريخ الإضافة</button>
        <button id="sort-by-name" class="control-btn info">ترتيب حسب الاسم</button>
      </div>
      <div id="no-chapters-novels" class="novels-grid"></div>
      <div class="no-chapters-empty" style="display: none;">
        <div class="empty-state">
          <i class="fas fa-book-open fa-3x"></i>
          <h3>لا توجد أعمال بدون فصول حاليًا</h3>
          <p>سيتم عرض الأعمال الجديدة هنا فور إضافتها</p>
        </div>
      </div>
    </div>

    <!-- نموذج إضافة رابط جديد -->
    <div id="add-link-form" class="add-link-form" style="display: none;">
      <div class="form-container">
        <h3>إضافة رابط جديد</h3>
        <div class="form-group">
          <label for="new-link">الرابط:</label>
          <input type="url" id="new-link" placeholder="أدخل رابط الرواية هنا" class="form-control">
        </div>
        <div class="form-actions">
          <button id="submit-link" class="control-btn start" onclick="addNewLink()">إضافة</button>
          <button id="cancel-link" class="control-btn stop" onclick="hideAddLinkForm()">إلغاء</button>
        </div>
        <div id="add-link-status" class="add-link-status"></div>
      </div>
    </div>

    <!-- نموذج إضافة معلومات الرواية -->
    <div id="novel-info-form" class="add-link-form" style="display: none;">
      <div class="form-container">
        <h3>إضافة معلومات الرواية</h3>
        <div id="novel-info-step-1">
          <div class="form-group">
            <label for="novel-url">رابط الرواية:</label>
            <input type="url" id="novel-url" placeholder="أدخل رابط صفحة الرواية هنا" class="form-control">
          </div>
          <div class="form-actions">
            <button id="fetch-novel-info" class="control-btn start" onclick="fetchNovelInfo()">جلب البيانات</button>
            <button id="cancel-novel-info" class="control-btn stop" onclick="hideNovelInfoForm()">إلغاء</button>
          </div>
        </div>
        <div id="novel-info-step-2" style="display: none;">
          <div class="form-group">
            <h4>معلومات الرواية:</h4>
            <div id="novel-info-preview" class="novel-info-preview">
              <!-- هنا سيتم عرض معلومات الرواية بعد جلبها -->
            </div>
          </div>
          <div class="form-actions">
            <button id="add-novel-info" class="control-btn start" onclick="addNovelInfo()">إضافة إلى قاعدة البيانات</button>
            <button id="back-novel-info" class="control-btn restart" onclick="backToNovelInfoStep1()">رجوع</button>
            <button id="cancel-novel-info-2" class="control-btn stop" onclick="hideNovelInfoForm()">إلغاء</button>
          </div>
        </div>
        <div id="novel-info-status" class="add-link-status"></div>
      </div>
    </div>

    <!-- عنوان قسم الأعمال التي لها فصول -->
    <div class="section-header chapters-header">
      <div class="section-separator">
        <div class="separator-line"></div>
        <div class="separator-icon"><i class="fas fa-book-reader"></i></div>
        <div class="separator-line"></div>
      </div>
      <h2 class="section-title">الأعمال التي لها فصول</h2>
    </div>

    <div id="novels" class="novels-grid"></div>

    <!-- أزرار التصفح -->
    <div class="pagination">
      <button id="prev-page" class="pagination-btn" disabled>السابق</button>
      <span class="pagination-info" id="pagination-info">صفحة 1 من 1</span>
      <button id="next-page" class="pagination-btn" disabled>التالي</button>
    </div>

    <script src="latest.js"></script>
</body>
</html>